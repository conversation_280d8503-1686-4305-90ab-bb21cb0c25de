@import url(/node_modules/reset-css/reset.css);

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-height: 100vh;
  min-width: 100vw;
}

.outer {
  height: 100vh;
  width: 3000px;
  
}

canvas {
  background-color: #21354734;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

 /* ----- Light / Dark -----  */
@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}


.slider {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  height: 100vw;
}


 /* Page Settings */


 section {
  height: 100vw;
  }

  .section1 {
    background-color: #9dedff;
    width: 1000px !important;
  }
  
  .section2 {
    background-color: #d09dff;
    width: 1000px !important;
  }
  
  .section3 {
    background-color: #ff9d9d;
    width: 1000px !important;
  }

  .inner {
    position: relative;
    /* margin: 1em; */
    /* height: calc(100% - 14em); */
    height: 500px;
    width: 500px;
    /* align-items: end; */
    bottom: 0px;
    background-color: #9a2fec;
  }

  .content {
    justify-self: flex-end;
  }

  p {
    width: 50%;
    line-height: 1.5;
    font-weight: 300;
  }


  canvas {
    outline: none;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0); /* mobile webkit */
  }


/*  Lenis  */

html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}