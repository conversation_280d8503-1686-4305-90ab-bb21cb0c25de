{"name": "vanilla-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@babylonjs/core": "^6.37.1", "@babylonjs/gui": "^6.37.1", "@babylonjs/gui-editor": "^6.37.1", "@babylonjs/inspector": "^6.37.1", "@babylonjs/loaders": "^6.37.1", "@babylonjs/materials": "^6.37.1", "@babylonjs/serializers": "^6.37.1", "@types/cannon": "^0.1.12", "moment": "^2.29.4", "reset-css": "^5.0.2", "typescript": "^5.2.2", "vite": "^5.0.8"}, "dependencies": {"cannon": "^0.6.2", "gsap": "^3.13.0", "lenis": "^1.3.4"}}